#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
新场站分析报告生成工具

此脚本专门用于为新场站坐标生成完整的分析报告，包括：
1. 坐标评估和分析
2. 可视化图表生成
3. LLM增强的详细报告
4. 自动打开浏览器查看

使用方法：
python new_station_report_generator.py --longitude 116.407526 --latitude 39.90403
python new_station_report_generator.py --longitude 121.473701 --latitude 31.230416 --radius 2000
"""

import os
import sys
import argparse
import webbrowser
from station_analysis import StationAnalyzer
from evaluate_coordinates import evaluate_coordinates, visualize_evaluation_results
from generate_report import generate_html_report
from report_templates import get_basic_templates
import config

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='新场站分析报告生成工具')
    
    parser.add_argument('--longitude', type=float, required=True,
                       help='经度（必需）')
    parser.add_argument('--latitude', type=float, required=True,
                       help='纬度（必需）')
    parser.add_argument('--radius', type=int, default=1000,
                       help='POI搜索半径（米），默认1000')
    parser.add_argument('--use_llm', action='store_true', default=True,
                       help='使用LLM生成详细报告内容（默认开启）')
    parser.add_argument('--template', choices=['basic', 'full'], default='basic',
                       help='报告模板类型，默认basic')
    parser.add_argument('--no_browser', action='store_true',
                       help='不自动打开浏览器')
    
    return parser.parse_args()

def generate_new_station_analysis_report(longitude, latitude, radius=1000, use_llm=True, template_type='basic', auto_open_browser=True):
    """
    为新场站生成完整的分析报告
    
    参数:
        longitude: 经度
        latitude: 纬度
        radius: POI搜索半径（米）
        use_llm: 是否使用LLM生成报告内容
        template_type: 模板类型
        auto_open_browser: 是否自动打开浏览器
    
    返回:
        (report_file, image_file): 报告文件路径和图片文件路径
    """
    print("=" * 60)
    print("新场站分析报告生成工具")
    print("=" * 60)
    print(f"分析坐标: ({longitude}, {latitude})")
    print(f"搜索半径: {radius}米")
    print(f"使用LLM: {'是' if use_llm else '否'}")
    print(f"模板类型: {template_type}")
    print("=" * 60)
    
    try:
        # 1. 初始化分析器
        print("步骤 1/5: 初始化分析器...")
        analyzer = StationAnalyzer(order_data_file='resource/2025_Q2_10cols_107stations.csv')
        
        # 2. 加载数据
        print("步骤 2/5: 加载数据...")
        analyzer.load_data()
        analyzer.build_poi_vectors()
        analyzer.calculate_similarity()
        analyzer.build_scoring_framework()
        
        # 3. 评估坐标
        print("步骤 3/5: 评估坐标...")
        gaode_api_key = config.get_api_key('gaode')
        evaluation_data = evaluate_coordinates(longitude, latitude, analyzer, api_key=gaode_api_key, radius=radius)
        
        if evaluation_data is None:
            print(f"错误: 无法评估坐标 ({longitude}, {latitude})")
            return None, None
        
        # 4. 生成可视化图片
        print("步骤 4/5: 生成可视化图片...")
        image_path = f"output/坐标评估_{longitude}_{latitude}.png"
        visualize_evaluation_results(evaluation_data, analyzer, save_path=image_path)
        
        # 5. 生成HTML报告
        print("步骤 5/5: 生成分析报告...")
        
        # 获取API密钥
        api_key = None
        if use_llm:
            try:
                api_key = config.get_api_key('deepseek')
            except:
                print("警告: 未找到DeepSeek API密钥，将使用模板生成报告")
                use_llm = False
        
        # 获取模板
        templates = get_basic_templates()
        
        # 生成报告
        report_file = generate_html_report(image_path, evaluation_data, use_llm, api_key, template_type)
        
        print("=" * 60)
        print("报告生成完成！")
        print(f"评估图片: {image_path}")
        print(f"分析报告: {report_file}")
        print("=" * 60)
        
        # 打印评估结果摘要
        print("评估结果摘要:")
        print(f"  POI评分: {evaluation_data.get('poi_score', 0):.2f}")
        if 'strategic_score' in evaluation_data:
            print(f"  战略价值评分: {evaluation_data.get('strategic_score', 0):.2f}")
        if evaluation_data.get('performance_score') is not None:
            print(f"  业绩评分: {evaluation_data.get('performance_score', 0):.2f}")
            print(f"  综合评分: {evaluation_data.get('combined_score', 0):.2f}")
        if evaluation_data.get('predicted_revenue') is not None:
            print(f"  预测周营业额: {evaluation_data.get('predicted_revenue', 0):.2f}元")
        print(f"  建议: {evaluation_data.get('recommendation', '未知')}建设充电站")
        
        # 自动打开浏览器
        if auto_open_browser:
            try:
                webbrowser.open(f"file://{os.path.abspath(report_file)}")
                print("报告已在浏览器中打开")
            except Exception as e:
                print(f"无法自动打开浏览器: {e}")
                print(f"请手动打开报告: {report_file}")
        
        return report_file, image_path
        
    except Exception as e:
        print(f"生成报告时出错: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """主函数"""
    args = parse_arguments()
    
    # 生成新场站分析报告
    report_file, image_file = generate_new_station_analysis_report(
        longitude=args.longitude,
        latitude=args.latitude,
        radius=args.radius,
        use_llm=args.use_llm,
        template_type=args.template,
        auto_open_browser=not args.no_browser
    )
    
    if report_file:
        print("\n✅ 新场站分析报告生成成功！")
        print(f"📊 评估图片: {image_file}")
        print(f"📄 分析报告: {report_file}")
    else:
        print("\n❌ 报告生成失败，请检查输入参数和配置")
        sys.exit(1)

if __name__ == "__main__":
    main()
