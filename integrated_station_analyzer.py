#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
集成距离权重的场站分析器
将距离权重POI计算融合到完整的场站评分体系中
"""

import os
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from collections import defaultdict
from station_analysis import StationAnalyzer

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

class IntegratedStationAnalyzer(StationAnalyzer):
    """集成距离权重的场站分析器，继承原有功能并增强POI计算"""
    
    def __init__(self, order_data_file='resource/2025_Q2_10cols_107stations.csv', 
                 order_data_file_q1='resource/output_2025_Q1_10cols_107stations.csv',
                 use_distance_weight=True, decay_factor=1000, max_distance=3000, min_weight=0.1):
        """初始化集成分析器
        
        参数:
            order_data_file: 第二季度订单数据文件路径
            order_data_file_q1: 第一季度订单数据文件路径
            use_distance_weight: 是否使用距离权重
            decay_factor: 距离衰减因子（米）
            max_distance: 最大有效距离（米）
            min_weight: 最小权重值
        """
        # 调用父类初始化
        super().__init__(order_data_file, order_data_file_q1)
        
        # 距离权重参数
        self.use_distance_weight = use_distance_weight
        self.decay_factor = decay_factor
        self.max_distance = max_distance
        self.min_weight = min_weight
        
        print(f"集成分析器初始化完成")
        if use_distance_weight:
            print(f"距离权重参数: 衰减因子={decay_factor}m, 最大距离={max_distance}m, 最小权重={min_weight}")
    
    def calculate_distance_weight(self, distance):
        """计算距离权重
        
        参数:
            distance: 距离（米）
            
        返回:
            距离权重 (min_weight ~ 1.0)
        """
        if not self.use_distance_weight:
            return 1.0
            
        if distance > self.max_distance:
            return self.min_weight
        
        # 使用指数衰减函数
        weight = np.exp(-distance / self.decay_factor)
        return max(self.min_weight, weight)
    
    def build_poi_vectors(self):
        """构建增强的POI向量，集成距离权重和避免重复计算"""
        print("正在构建集成距离权重的POI向量...")
        
        # 创建一个字典，用于存储每个场站的POI向量
        station_poi_vectors = {}
        
        # 对于每个场站
        for station_name, station_info in self.raw_poi_data.items():
            # 初始化POI向量
            poi_vector = defaultdict(float)  # 使用float支持距离权重
            
            # 获取场站周围的POI数据
            pois = station_info['data'].get('pois', [])
            
            # 统计每个POI类别的加权数量
            for poi in pois:
                if 'typecode' in poi:
                    typecode = poi['typecode']
                    
                    # 计算距离权重
                    distance = float(poi.get('distance', 0))
                    distance_weight = self.calculate_distance_weight(distance)
                    
                    # 获取大类和中类编码
                    if len(typecode) == 6:  # 确保是6位编码
                        large_category = typecode[:2] + '0000'  # 大类
                        medium_category = typecode[:4] + '00'   # 中类
                        
                        # 优先使用最具体的编码，避免重复计算
                        if typecode in self.used_poi_codes:
                            poi_vector[typecode] += distance_weight
                        elif medium_category in self.used_poi_codes:
                            poi_vector[medium_category] += distance_weight
                        elif large_category in self.used_poi_codes:
                            poi_vector[large_category] += distance_weight
            
            # 将defaultdict转换为普通dict并存储
            station_poi_vectors[station_name] = dict(poi_vector)
        
        # 创建一个DataFrame，行是场站，列是POI类别
        all_typecodes = sorted(self.used_poi_codes)
        stations = sorted(station_poi_vectors.keys())
        
        # 创建一个空的DataFrame
        self.poi_vectors = pd.DataFrame(0.0, index=stations, columns=all_typecodes)
        
        # 填充DataFrame
        for station in stations:
            for typecode, weighted_count in station_poi_vectors[station].items():
                if typecode in all_typecodes:
                    self.poi_vectors.at[station, typecode] = weighted_count
        
        print(f"已为 {len(stations)} 个场站构建集成POI向量，包含 {len(all_typecodes)} 个POI类别")
        print(f"距离权重: {'启用' if self.use_distance_weight else '禁用'}")
        
        return self.poi_vectors
    
    def build_integrated_scoring_framework(self, calculate_strategic=True):
        """构建集成的多维度评分框架"""
        print("正在构建集成的多维度评分框架...")
        
        # 构建增强的POI向量
        if self.poi_vectors is None:
            self.build_poi_vectors()
        
        # 如果有订单数据，尝试优化权重
        if self.order_analyzer is not None:
            self.optimize_weights_with_orders()
        
        # 为每个场站计算POI评分
        poi_scores = {}
        for station in self.poi_vectors.index:
            poi_scores[station] = self.evaluate_station_score(station)
        
        # 计算战略价值评分（如果需要）
        if calculate_strategic:
            strategic_scores = self.calculate_and_save_strategic_scores()
        else:
            # 尝试从缓存加载战略价值评分
            import os
            import json
            cache_file = 'output/strategic_scores.json'
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        strategic_scores = json.load(f)
                    print(f"已从缓存加载 {len(strategic_scores)} 个充电站的战略价值评分")
                    self.strategic_scores = strategic_scores
                except Exception as e:
                    print(f"加载缓存文件失败: {e}")
                    strategic_scores = {station: 50 for station in self.poi_vectors.index}
                    self.strategic_scores = strategic_scores
            else:
                print("未找到战略价值评分缓存，使用默认值")
                strategic_scores = {station: 50 for station in self.poi_vectors.index}
                self.strategic_scores = strategic_scores
        
        # 如果有订单数据，计算业绩评分
        if self.order_analyzer is not None and self.performance_scores is not None:
            # 创建DataFrame，包含POI评分、业绩评分和战略价值评分
            stations = list(poi_scores.keys())
            poi_score_list = list(poi_scores.values())
            strategic_score_list = [strategic_scores.get(station, 0) for station in stations]
            
            # 获取业绩评分（如果有）
            performance_scores = []
            for station in stations:
                if station in self.performance_scores:
                    performance_scores.append(self.performance_scores[station])
                else:
                    performance_scores.append(None)
            
            # 计算综合评分（POI评分、业绩评分和战略价值评分的加权平均）
            combined_scores = []
            for i in range(len(stations)):
                if performance_scores[i] is not None:
                    # 综合评分 = 0.5 * POI评分 + 0.3 * 业绩评分 + 0.2 * 战略价值评分
                    combined_scores.append(
                        0.5 * poi_score_list[i] +
                        0.3 * performance_scores[i] +
                        0.2 * strategic_score_list[i]
                    )
                else:
                    # 如果没有业绩评分，综合评分为POI评分和战略价值评分的加权平均
                    combined_scores.append(0.7 * poi_score_list[i] + 0.3 * strategic_score_list[i])
            
            self.scores_df = pd.DataFrame({
                'station': stations,
                'poi_score': poi_score_list,
                'performance_score': performance_scores,
                'strategic_score': strategic_score_list,
                'combined_score': combined_scores
            })
            
            # 按综合得分降序排序
            self.scores_df = self.scores_df.sort_values('combined_score', ascending=False)
        else:
            # 创建DataFrame，包含POI评分和战略价值评分
            stations = list(poi_scores.keys())
            poi_score_list = list(poi_scores.values())
            strategic_score_list = [strategic_scores.get(station, 0) for station in stations]
            
            # 计算综合评分（POI评分和战略价值评分的加权平均）
            combined_scores = []
            for i in range(len(stations)):
                combined_scores.append(0.7 * poi_score_list[i] + 0.3 * strategic_score_list[i])
            
            self.scores_df = pd.DataFrame({
                'station': stations,
                'poi_score': poi_score_list,
                'strategic_score': strategic_score_list,
                'combined_score': combined_scores
            })
            
            # 按综合得分降序排序
            self.scores_df = self.scores_df.sort_values('combined_score', ascending=False)
        
        print("集成多维度评分框架构建完成")
        return self.scores_df
    
    def compare_with_original_method(self):
        """与原有方法对比"""
        print("\n对比集成方法与原有方法...")
        
        # 保存当前设置
        current_use_distance = self.use_distance_weight
        
        # 构建原有方法的POI向量（不使用距离权重）
        print("构建原有方法POI向量...")
        self.use_distance_weight = False
        original_vectors = self.build_poi_vectors()
        
        # 计算原有方法评分
        original_scores = []
        for station in original_vectors.index:
            score = self.evaluate_station_score(station)
            original_scores.append(score)
        
        # 恢复距离权重设置并构建集成方法向量
        print("构建集成方法POI向量...")
        self.use_distance_weight = current_use_distance
        integrated_vectors = self.build_poi_vectors()
        
        # 计算集成方法评分
        integrated_scores = []
        for station in integrated_vectors.index:
            score = self.evaluate_station_score(station)
            integrated_scores.append(score)
        
        # 对比分析
        self._analyze_comparison(original_scores, integrated_scores, original_vectors, integrated_vectors)
        
        return original_scores, integrated_scores
    
    def _analyze_comparison(self, original_scores, integrated_scores, original_vectors, integrated_vectors):
        """分析对比结果"""
        # 计算统计指标
        original_stats = pd.Series(original_scores).describe()
        integrated_stats = pd.Series(integrated_scores).describe()
        correlation = pd.Series(original_scores).corr(pd.Series(integrated_scores))
        
        print("\n评分对比分析:")
        print(f"{'指标':<10} {'原有方法':<12} {'集成方法':<12} {'变化':<10}")
        print("-" * 50)
        print(f"{'平均分':<10} {original_stats['mean']:<12.2f} {integrated_stats['mean']:<12.2f} {(integrated_stats['mean']-original_stats['mean'])/original_stats['mean']*100:>8.1f}%")
        print(f"{'标准差':<10} {original_stats['std']:<12.2f} {integrated_stats['std']:<12.2f} {(integrated_stats['std']-original_stats['std'])/original_stats['std']*100:>8.1f}%")
        print(f"{'最大值':<10} {original_stats['max']:<12.2f} {integrated_stats['max']:<12.2f} {(integrated_stats['max']-original_stats['max'])/original_stats['max']*100:>8.1f}%")
        print(f"{'相关性':<10} {'-':<12} {correlation:<12.3f} {'-':<10}")
        
        # 分析POI向量差异
        vector_diff = integrated_vectors.sum(axis=1) - original_vectors.sum(axis=1)
        avg_reduction = vector_diff.mean()
        print(f"\nPOI向量分析:")
        print(f"平均POI数量减少: {-avg_reduction:.2f}")
        print(f"距离权重影响的站点: {(vector_diff < 0).sum()} / {len(vector_diff)}")
        
        # 保存对比结果
        self._save_comparison_results(original_scores, integrated_scores, correlation)
    
    def _save_comparison_results(self, original_scores, integrated_scores, correlation):
        """保存对比结果"""
        # 生成对比报告
        report = []
        report.append("# 集成距离权重的场站评分体系报告\n")
        
        report.append("## 1. 集成内容\n")
        report.append("本系统将距离权重POI计算集成到完整的场站评分体系中，包括：")
        report.append("- **距离权重POI计算**: 根据POI距离应用衰减权重")
        report.append("- **避免重复计算**: 优先使用最具体的POI编码")
        report.append("- **多维度评分**: POI评分 + 业绩评分 + 战略价值评分")
        report.append("- **权重优化**: 基于订单数据的机器学习优化\n")
        
        report.append("## 2. 距离权重参数\n")
        report.append(f"- 衰减因子: {self.decay_factor}米")
        report.append(f"- 最大距离: {self.max_distance}米")
        report.append(f"- 最小权重: {self.min_weight}")
        report.append(f"- 权重公式: weight = exp(-distance/{self.decay_factor})\n")
        
        # 评分对比
        original_stats = pd.Series(original_scores).describe()
        integrated_stats = pd.Series(integrated_scores).describe()
        
        report.append("## 3. 评分效果对比\n")
        report.append("| 指标 | 原有方法 | 集成方法 | 变化幅度 |")
        report.append("|------|----------|----------|----------|")
        report.append(f"| 平均分 | {original_stats['mean']:.2f} | {integrated_stats['mean']:.2f} | {(integrated_stats['mean']-original_stats['mean'])/original_stats['mean']*100:+.1f}% |")
        report.append(f"| 标准差 | {original_stats['std']:.2f} | {integrated_stats['std']:.2f} | {(integrated_stats['std']-original_stats['std'])/original_stats['std']*100:+.1f}% |")
        report.append(f"| 评分相关性 | - | - | {correlation:.3f} |\n")
        
        report.append("## 4. 主要优势\n")
        report.append("1. **更准确的POI评分**: 考虑距离因素，更符合实际使用情况")
        report.append("2. **完整的评分体系**: 集成POI、业绩、战略价值三个维度")
        report.append("3. **自动权重优化**: 基于实际数据优化POI权重")
        report.append("4. **保持兼容性**: 可以无缝替换原有系统")
        report.append("5. **参数可调**: 距离权重参数可根据业务需求调整\n")
        
        report.append("## 5. 使用建议\n")
        report.append("1. **生产环境部署**: 建议替换原有的POI计算方法")
        report.append("2. **参数调优**: 根据实际业务数据调整距离权重参数")
        report.append("3. **定期更新**: 定期使用最新订单数据优化权重")
        report.append("4. **效果监控**: 监控评分与实际业绩的相关性\n")
        
        # 保存报告
        os.makedirs('output', exist_ok=True)
        with open('output/integrated_station_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print("集成分析报告已保存到 output/integrated_station_analysis_report.md")
    
    def save_integrated_results(self):
        """保存集成分析结果"""
        os.makedirs('output', exist_ok=True)
        
        # 保存POI向量
        if self.poi_vectors is not None:
            self.poi_vectors.to_csv('output/integrated_poi_vectors.csv')
            print("集成POI向量已保存到 output/integrated_poi_vectors.csv")
        
        # 保存评分结果
        if hasattr(self, 'scores_df') and self.scores_df is not None:
            self.scores_df.to_csv('output/integrated_station_scores.csv', index=False)
            print("集成评分结果已保存到 output/integrated_station_scores.csv")

def main():
    """主函数，演示集成分析器"""
    print("集成距离权重的场站评分体系演示")
    print("=" * 60)
    
    # 创建集成分析器
    analyzer = IntegratedStationAnalyzer(
        use_distance_weight=True,
        decay_factor=1000,
        max_distance=3000,
        min_weight=0.1
    )
    
    # 加载数据
    analyzer.load_data()
    
    # 构建集成评分框架
    scores_df = analyzer.build_integrated_scoring_framework()
    
    # 显示前10名场站
    print("\n集成评分前10名场站:")
    print(scores_df.head(10)[['station', 'poi_score', 'combined_score']])
    
    # 与原有方法对比
    analyzer.compare_with_original_method()
    
    # 保存结果
    analyzer.save_integrated_results()
    
    # 可视化评分
    analyzer.visualize_scores()
    
    print("\n集成分析演示完成！")

if __name__ == "__main__":
    main()
