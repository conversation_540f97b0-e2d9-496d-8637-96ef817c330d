#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
充电站评估报告提示词模板

此模块定义了用于生成充电站评估报告的提示词模板，确保LLM返回格式一致的内容
"""

import json

# 基础提示词模板 - 包含通用的格式要求和指导
BASE_PROMPT_TEMPLATE = """
你是一位充电站选址专家，请根据以下评估数据，生成一份充电站评估报告的"{section_name}"部分。

评估数据:
{evaluation_data}

请参考以下模板格式，但内容要更加详细、专业，并提供有价值的洞察:
{template}

要求:
1. 内容要专业、详细，体现出对充电站选址的专业知识；尤其需要注意这是移动充电车的充电站，区别于一般的固定充电站，侧重点不同
2. 根据评估数据给出具体、可操作的建议；深度结合数据，不要过于发散思维
3. 分析要有深度，不要泛泛而谈
4. 内容格式应为HTML，可以包含列表、表格等元素，但不要包含HTML、BODY等标签
5. 返回的内容将直接插入到HTML报告中，请确保格式正确
6. 目前移动充电车只有两种规格：1.额定电量184kWh，额定电压614.4V，额定容量300Ah，功率60kW，充电电流最大150A 2.额定电量208.998kWh，额定电压665.6V，额定容量314Ah，功率150kW，充电电流最大250A
7. 请严格按照以下格式规范生成内容:
   - 使用<h2>、<h3>、<h4>标签作为标题层级
   - 使用<p>标签包裹段落文本
   - 使用<ul>/<li>或<ol>/<li>标签创建列表
   - 使用<table>/<tr>/<th>/<td>标签创建表格
   - 使用<strong>标签强调重要内容
   - 使用<div class="section-name">包裹各个部分
8. 每个部分的内容结构应保持一致，包括标题、正文、数据分析和建议
"""

# 各部分特定的提示词模板
SECTION_SPECIFIC_PROMPTS = {
    "概览": """
除了基本要求外，概览部分应包含以下内容和格式:
- 使用<h2>评估概览</h2>作为标题
- 包含基础信息部分，使用<div class="overview-section">包裹
- 包含评分分析部分，使用表格展示各项评分
- 包含财务预测部分，展示预测周营业额和建议配置
- 包含专业建议部分，明确说明是否适合建设充电站
- 使用统一的格式展示数据：评分保留2位小数，金额使用逗号分隔千位
""",

    "POI分析": """
除了基本要求外，POI分析部分应包含以下内容和格式:
- 使用<h3>POI评分深度分析</h3>作为标题
- 解释POI评分的含义和重要性
- 使用表格对比当前站点与区域标杆值
- 分析同类站点的POI特征
- 提供基于POI特征的设备配置建议
- 提供运营优化建议
- 使用统一的格式展示数据：评分保留2位小数
""",

    "战略价值分析": """
除了基本要求外，战略价值分析部分应包含以下内容和格式:
- 使用<h3>战略价值深度分析</h3>作为标题
- 解释战略价值评分的计算维度
- 使用表格展示竞品网络对比
- 提供移动充电适配建议
- 包含风险提示部分
- 使用统一的格式展示数据：评分保留2位小数
""",

    "业绩分析": """
除了基本要求外，业绩分析部分应包含以下内容和格式:
- 使用<h3>业绩分析与预测</h3>作为标题
- 使用表格展示关键业绩指标
- 包含相似场站对标分析
- 提供移动充电车专项建议
- 包含明确的结论
- 使用统一的格式展示数据：评分保留2位小数，金额使用¥符号
""",

    "综合评分分析": """
除了基本要求外，综合评分分析部分应包含以下内容和格式:
- 使用<h3>综合评分深度分析</h3>作为标题
- 使用表格展示核心指标解析
- 包含同类站点对比
- 提供移动充电车专项建议
- 包含风险提示
- 使用统一的格式展示数据：评分保留2位小数，金额使用¥符号
""",

    "相似场站分析": """
除了基本要求外，相似场站分析部分应包含以下内容和格式:
- 使用<h2>相似场站分析</h2>作为标题
- 使用表格展示相似场站数据，包含场站名称、相似度、战略评分和关键特征
- 包含关键洞察与建议部分
- 使用统一的格式展示数据：相似度使用百分比格式，评分保留1位小数
""",

    "建议与结论": """
除了基本要求外，建议与结论部分应包含以下内容和格式:
- 使用<h2>建议与结论</h2>作为标题
- 使用表格展示核心评估指标分析
- 包含运营优化建议，使用有序列表
- 包含风险提示，使用无序列表
- 包含最终结论，使用<strong>标签强调
- 使用统一的格式展示数据：评分保留2位小数，金额使用¥符号
"""
}

def get_prompt_for_section(section_name, evaluation_data, template):
    """
    获取特定部分的完整提示词
    
    参数:
        section_name: 部分名称
        evaluation_data: 评估数据（字典）
        template: 模板内容
        
    返回:
        完整的提示词
    """
    # 将评估数据转换为JSON字符串
    evaluation_data_str = json.dumps(evaluation_data, ensure_ascii=False, indent=2)
    
    # 获取基础提示词
    prompt = BASE_PROMPT_TEMPLATE.format(
        section_name=section_name,
        evaluation_data=evaluation_data_str,
        template=template
    )
    
    # 添加特定部分的提示词
    if section_name in SECTION_SPECIFIC_PROMPTS:
        prompt += "\n" + SECTION_SPECIFIC_PROMPTS[section_name]
    
    return prompt
